
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>PIE Workflow Animation — Interactive ML Pipeline Demo</title>
  <style>
    :root{
      --bg:#0b1220; --text:#e6ecff; --muted:#9fb2ff; --panel:#0f1832; --panel2:#131f3b;
      --cyan:#22d3ee; --amber:#f59e0b; --pink:#f472b6; --green:#4ade80; --glow:#3fa9ff;
      --purple:#8b5cf6; --orange:#f97316; --emerald:#10b981;
    }
    *{box-sizing:border-box}
    html,body{height:100%; overflow-x:hidden}
    body{margin:0; font-family:Inter,ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial;
      background: radial-gradient(1200px 800px at 65% -10%, #1a2240 0%, #0b1220 60%), var(--bg);
      color:var(--text); display:flex; align-items:center; justify-content:center; padding:24px; min-height:100vh}
    .wrap{width:min(1400px,95vw); position:relative}
    .title{ text-align:center; font-weight:800; font-size:clamp(24px,3vw,40px); margin-bottom:8px;
      background: linear-gradient(135deg, var(--cyan), var(--purple), var(--amber));
      -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
    .subtitle{ text-align:center; color:var(--muted); margin-bottom:24px; font-size:16px; line-height:1.5 }

    /* Layout: controls + pipeline */
    .top{display:grid; grid-template-columns:1.2fr .8fr; gap:20px; margin-bottom:24px}
    .card{background:linear-gradient(180deg,rgba(255,255,255,.08),rgba(255,255,255,.03));
      border:1px solid rgba(255,255,255,.12); border-radius:16px; padding:18px;
      box-shadow:0 12px 32px rgba(0,0,0,.4); backdrop-filter:blur(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position:relative; overflow:hidden}
    .card::before{content:''; position:absolute; top:0; left:0; right:0; height:2px;
      background:linear-gradient(90deg, var(--cyan), var(--purple), var(--amber)); opacity:0.6}
    .card:hover{transform:translateY(-2px); box-shadow:0 16px 40px rgba(0,0,0,.5)}
    .card h3{margin:0 0 12px; font-size:16px; letter-spacing:.3px; color:var(--cyan); font-weight:700}
    textarea, input{width:100%; background:var(--panel2); color:var(--text); border:1px solid rgba(255,255,255,.15);
      border-radius:12px; padding:14px; font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
      font-size:13px; transition:all 0.2s ease; line-height:1.4}
    textarea{height:140px; resize:vertical}
    textarea:focus, input:focus{outline:none; border-color:var(--cyan); box-shadow:0 0 0 3px rgba(34,211,238,.1)}
    .row{display:flex; gap:10px; align-items:center; flex-wrap:wrap}
    .btn{background:linear-gradient(135deg, #1e293b, #334155); color:var(--text); border:1px solid rgba(255,255,255,.2);
      padding:12px 18px; border-radius:12px; cursor:pointer; font-weight:700; font-size:14px;
      transition:all 0.2s cubic-bezier(0.4, 0, 0.2, 1); position:relative; overflow:hidden}
    .btn:hover{transform:translateY(-1px); box-shadow:0 8px 20px rgba(0,0,0,.3)}
    .btn:active{transform:translateY(0)}
    .btn.primary{background:linear-gradient(135deg, var(--cyan), var(--purple)); border-color:var(--cyan)}
    .btn.primary:hover{box-shadow:0 8px 25px rgba(34,211,238,.3)}

    /* Pipeline grid with better spacing and wider columns */
    .pipeline{position:relative; display:grid; grid-template-columns:1.2fr auto 1.2fr auto 1.2fr auto 1.2fr auto 1.4fr;
      align-items:stretch; gap:16px; margin:20px 0; padding:20px 0}
    .step{position:relative; border-radius:20px; padding:18px;
      background:linear-gradient(180deg,rgba(255,255,255,.08),rgba(255,255,255,.03));
      border:1px solid rgba(255,255,255,.12); box-shadow:0 8px 24px rgba(0,0,0,.4);
      min-height:160px; min-width:180px; transition:all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter:blur(10px); overflow:hidden; word-wrap:break-word}
    .step::before{content:''; position:absolute; top:0; left:0; right:0; height:3px;
      background:linear-gradient(90deg, transparent, var(--cyan), transparent); opacity:0;
      transition:opacity 0.3s ease}
    .step.active::before{opacity:1}
    .step .head{display:flex; align-items:center; gap:10px; font-weight:800; font-size:15px; margin-bottom:8px;
      line-height:1.2; flex-wrap:wrap}
    .step .head div{flex:1; min-width:0; word-break:break-word}
    .step .emoji{font-size:26px; filter:drop-shadow(0 2px 4px rgba(0,0,0,.3)); flex-shrink:0}
    .step .meta{color:var(--muted); font-size:12px; margin-top:4px; line-height:1.3}

    /* Enhanced arrows with better animation */
    .arrow{align-self:center; width:80px; height:8px; border-radius:999px;
      background:rgba(255,255,255,.15); overflow:hidden; border:1px solid rgba(255,255,255,.1);
      position:relative; box-shadow:inset 0 1px 2px rgba(0,0,0,.2)}
    .arrow::before{content:""; position:absolute; width:120%; height:100%;
      background:repeating-linear-gradient(90deg, rgba(34,211,238,.6) 0 12px, transparent 12px 24px);
      animation:slide 1.5s linear infinite; opacity:0; transition:opacity 0.3s ease}
    .arrow.active::before{opacity:1}
    .arrow::after{content:""; position:absolute; right:-10px; top:50%; transform:translateY(-50%);
      border-left:12px solid rgba(34,211,238,.8); border-top:8px solid transparent;
      border-bottom:8px solid transparent; opacity:0; transition:opacity 0.3s ease}
    .arrow.active::after{opacity:1}
    @keyframes slide{from{transform:translateX(-24px)} to{transform:translateX(0)}}

    /* Enhanced visuals inside steps */
    .jsonview{height:100px; overflow:auto; background:var(--panel2); border:1px solid rgba(255,255,255,.12);
      border-radius:12px; padding:12px; font-size:12px; line-height:1.4;
      box-shadow:inset 0 2px 4px rgba(0,0,0,.2); transition:all 0.3s ease}
    .jsonview:hover{border-color:rgba(255,255,255,.2)}

    /* Animated bars with stagger effect */
    .bars{display:flex; gap:8px; align-items:flex-end; height:90px; margin-top:12px; padding:0 4px}
    .bar{width:22px; background:linear-gradient(180deg, rgba(34,211,238,.4), rgba(34,211,238,.8));
      border-radius:8px 8px 4px 4px; transform:scaleY(.15); transform-origin:bottom;
      transition:transform 1s cubic-bezier(.2,.7,.2,1); position:relative; box-shadow:0 2px 8px rgba(34,211,238,.2)}
    .bar::before{content:''; position:absolute; top:-2px; left:0; right:0; height:2px;
      background:rgba(34,211,238,1); border-radius:2px; opacity:0; transition:opacity 0.3s ease}
    .bar.animating::before{opacity:1}
    .bar.yellow{background:linear-gradient(180deg, rgba(245,158,11,.4), rgba(245,158,11,.9));
      box-shadow:0 2px 8px rgba(245,158,11,.2)}
    .bar.yellow::before{background:rgba(245,158,11,1)}

    /* Enhanced chips with better animations */
    .chips{display:flex; flex-wrap:wrap; gap:8px; margin-top:12px}
    .chip{font-size:12px; padding:8px 12px; border-radius:20px; border:1px solid rgba(255,255,255,.15);
      background:#111a33; transition:all 0.3s cubic-bezier(0.4, 0, 0.2, 1); cursor:default;
      position:relative; overflow:hidden}
    .chip::before{content:''; position:absolute; top:0; left:-100%; width:100%; height:100%;
      background:linear-gradient(90deg, transparent, rgba(255,255,255,.1), transparent);
      transition:left 0.5s ease}
    .chip.hit{outline:2px solid var(--green); background:#0f2a1b; color:var(--green);
      box-shadow:0 0 20px rgba(74,222,128,.2); animation:chipGlow 2s ease-in-out infinite}
    .chip.hit::before{left:100%}
    @keyframes chipGlow{0%,100%{box-shadow:0 0 20px rgba(74,222,128,.2)} 50%{box-shadow:0 0 30px rgba(74,222,128,.4)}}

    .score{font-size:13px; color:var(--muted); margin-top:8px; font-weight:500}
    .bot{display:flex; align-items:flex-start; gap:12px}
    .bubble{background:linear-gradient(135deg, #0e1c38, #1a2332); border:1px solid rgba(255,255,255,.15);
      border-radius:16px; padding:12px 16px; font-size:14px; box-shadow:0 8px 20px rgba(0,0,0,.4);
      position:relative; overflow:hidden; line-height:1.4}
    .bubble::before{content:''; position:absolute; top:0; left:0; right:0; height:1px;
      background:linear-gradient(90deg, var(--cyan), var(--purple)); opacity:0.6}
    .hidden{display:none}

    /* Enhanced Active/Glow states with better animations */
    .step.active{
      box-shadow:0 16px 40px rgba(0,0,0,.6), 0 0 0 3px rgba(63,169,255,.3);
      transform:translateY(-4px) scale(1.02);
      background:linear-gradient(180deg,rgba(255,255,255,.12),rgba(255,255,255,.06));
    }
    .step.input.active{outline:3px solid rgba(34,211,238,.4); box-shadow:0 16px 40px rgba(0,0,0,.6), 0 0 30px rgba(34,211,238,.3)}
    .step.tfidf.active{outline:3px solid rgba(245,158,11,.4); box-shadow:0 16px 40px rgba(0,0,0,.6), 0 0 30px rgba(245,158,11,.3)}
    .step.intentmatch.active{outline:3px solid rgba(74,222,128,.4); box-shadow:0 16px 40px rgba(0,0,0,.6), 0 0 30px rgba(74,222,128,.3)}
    .step.nb.active{outline:3px solid rgba(244,114,182,.4); box-shadow:0 16px 40px rgba(0,0,0,.6), 0 0 30px rgba(244,114,182,.3)}
    .step.final.active{outline:3px solid rgba(139,92,246,.4); box-shadow:0 16px 40px rgba(0,0,0,.6), 0 0 30px rgba(139,92,246,.3)}

    /* Progress indicator */
    .progress-bar{
      position:absolute; top:0; left:0; height:4px; background:var(--cyan);
      width:0%; transition:width 0.3s ease; border-radius:0 4px 4px 0;
      box-shadow:0 0 10px rgba(34,211,238,.5)
    }

    /* Loading states */
    .step.loading .emoji{animation:bounce 1s ease-in-out infinite}
    @keyframes bounce{0%,100%{transform:translateY(0)} 50%{transform:translateY(-8px)}}

    /* Pulse effect for active elements */
    .step.active .emoji{animation:pulse 2s ease-in-out infinite}
    @keyframes pulse{0%,100%{transform:scale(1)} 50%{transform:scale(1.1)}}

    .note{color:var(--muted); font-size:14px; text-align:center; margin-top:16px; line-height:1.5;
      background:rgba(255,255,255,.03); padding:12px 20px; border-radius:12px;
      border:1px solid rgba(255,255,255,.08)}

    /* Enhanced mobile responsiveness */
    @media(max-width:900px){
      .top{grid-template-columns:1fr; gap:16px}
      .pipeline{grid-template-columns:1fr; gap:12px}
      .arrow{display:none}
      .step{min-height:140px; padding:16px; min-width:auto}
      .step .head{font-size:14px; gap:8px}
      .step .emoji{font-size:22px}
      .step .meta{font-size:11px}
      .bars{height:70px}
      .bar{width:18px}
    }

    @media(max-width:1200px) {
      .pipeline{grid-template-columns:1fr auto 1fr auto 1fr auto 1fr auto 1.2fr; gap:14px}
      .step{min-width:160px; padding:16px}
      .step .head{font-size:14px}
    }
  </style>
</head>
<body>
  <div class="wrap">
    <div class="progress-bar" id="progressBar"></div>
    <div class="title">🧠 PIE Workflow Animation</div>
    <div class="subtitle">Interactive Machine Learning Pipeline Demo<br>
      Watch as your test data flows through <b>TF-IDF vectorization</b> → <b>Intent Classification</b> → <b>Naive Bayes</b> → <b>Smart Response</b></div>

    <div class="top">
      <div class="card">
        <h3>📝 test_results.json</h3>
        <textarea id="jsonIn" spellcheck="false">{
  "runs": 1,
  "summary": {"passed": 42, "failed": 3, "skipped": 1, "duration_ms": 128734},
  "tests": [
    {"id":"t1","title":"checkout flow - happy path","status":"passed","duration_ms": 1543},
    {"id":"t2","title":"login shows error on bad creds","status":"failed","error":"AssertionError: expected 401","duration_ms": 932},
    {"id":"t3","title":"search returns results","status":"passed","duration_ms": 621}
  ]
}</textarea>
        <div class="row" style="margin-top:12px">
          <button class="btn" id="pretty">✨ Pretty‑print</button>
          <button class="btn" id="minify">🗜️ Minify</button>
        </div>
      </div>
      <div class="card">
        <h3>💬 User Question</h3>
        <input id="q" value="how many tests passed?" placeholder="Ask about your test results..." />
        <div class="row" style="margin-top:12px">
          <button class="btn primary" id="run">🚀 Run Pipeline</button>
          <button class="btn" id="reset">🔄 Reset</button>
          <button class="btn" id="autoplay">⚡ Auto Demo</button>
        </div>
        <div class="note">💡 Try different questions or use Auto Demo mode for continuous demonstration.</div>
      </div>
    </div>

    <div class="pipeline" id="pipe">
      <!-- 1: Data Input -->
      <div class="step input" id="s1">
        <div class="head"><span class="emoji">📝</span><div>Data Input</div></div>
        <div class="meta">Parse and load test results from JSON</div>
        <div class="jsonview" id="jsonView">(ready to process...)</div>
      </div>
      <div class="arrow" id="arrow1"></div>

      <!-- 2: TF‑IDF (data + query) -->
      <div class="step tfidf" id="s2">
        <div class="head"><span class="emoji">🔄</span><div>TF‑IDF Vectorization</div></div>
        <div class="meta">Transform text into numerical vectors</div>
        <div class="bars">
          <div class="bar" id="b1"></div>
          <div class="bar yellow" id="b2"></div>
          <div class="bar" id="b3"></div>
          <div class="bar yellow" id="b4"></div>
          <div class="bar" id="b5"></div>
          <div class="bar yellow" id="b6"></div>
        </div>
        <div class="score" id="simScore">Similarity Score: —</div>
      </div>
      <div class="arrow" id="arrow2"></div>

      <!-- 3: Intent match (semantic label) -->
      <div class="step intentmatch" id="s3">
        <div class="head"><span class="emoji">🎯</span><div>Intent Classification</div></div>
        <div class="meta">Identify user's question type using ML</div>
        <div class="chips" id="chips"></div>
        <div class="score" id="intentScore">Best Match: —</div>
      </div>
      <div class="arrow" id="arrow3"></div>

      <!-- 4: Naive Bayes retrieval & Bot output -->
      <div class="step nb" id="s4">
        <div class="head"><span class="emoji">🧮</span><div>Response Generation</div></div>
        <div class="meta">Generate intelligent answer using Naive Bayes</div>
        <div class="bot" style="margin-top:12px">
          <div class="emoji">🤖</div>
          <div class="bubble" id="botOut">Ready to help! Ask me anything about your tests.</div>
        </div>
      </div>
      <div class="arrow" id="arrow4"></div>

      <!-- 5: Final (for stats/insights) -->
      <div class="step final" id="s5">
        <div class="head"><span class="emoji">📊</span><div>Analytics</div></div>
        <div class="meta">Extract insights and metrics</div>
        <div class="jsonview" id="insight">(insights will appear here)</div>
      </div>
    </div>

    <div class="note">🎬 Watch the complete ML pipeline in action with real-time animations, progress tracking, and intelligent responses!</div>
  </div>

  <script>
    // DOM Elements
    const jsonIn = document.getElementById('jsonIn');
    const jsonView = document.getElementById('jsonView');
    const qEl = document.getElementById('q');
    const progressBar = document.getElementById('progressBar');

    const steps = [
      document.getElementById('s1'),
      document.getElementById('s2'),
      document.getElementById('s3'),
      document.getElementById('s4'),
      document.getElementById('s5')
    ];
    const arrows = [1,2,3,4].map(i => document.getElementById('arrow'+i));

    const bars = [1,2,3,4,5,6].map(i=> document.getElementById('b'+i));
    const simScore = document.getElementById('simScore');
    const chipsBox = document.getElementById('chips');
    const intentScore = document.getElementById('intentScore');
    const botOut = document.getElementById('botOut');
    const insight = document.getElementById('insight');

    let isRunning = false;
    let currentStep = 0;

    const intents = [
      { id:'pass_count',   label:'Pass Count',   ex:['how many passed','passed tests','tests passed','count pass'], tpl:(data)=>`There are <b>${data.summary.passed}</b> tests passed.` },
      { id:'fail_count',   label:'Fail Count',   ex:['how many failed','failed tests','count fail','what failed'], tpl:(data)=>`You have <b>${data.summary.failed}</b> failing tests.` },
      { id:'duration',     label:'Total Duration', ex:['total time','how long','duration'], tpl:(data)=>`Total run time: <b>${Math.round(data.summary.duration_ms/1000)}s</b>.` },
      { id:'flaky',        label:'Flaky/Intermittent', ex:['flaky','intermittent','sometimes fails'], tpl:(data)=>`Looking for flakiness across retries (demo: none flagged here).` }
    ];

    // Utilities
    const sleep = (ms)=> new Promise(r=> setTimeout(r, ms));
    const pretty = (o)=> JSON.stringify(o, null, 2);

    function cosineSimilarity(a,b){
      const dot = a.reduce((s,v,i)=> s + v*(b[i]||0), 0);
      const na = Math.sqrt(a.reduce((s,v)=> s+v*v,0));
      const nb = Math.sqrt(b.reduce((s,v)=> s+v*v,0));
      return na && nb ? dot/(na*nb) : 0;
    }

    function tfidfVector(tokens, vocab){
      // tiny fake tf-idf: term frequency / sqrt(len) with idf weights
      const idf = {}; vocab.forEach(t=> idf[t] = Math.log(10/(1+ (t.length%5))) + 1);
      const counts = {}; tokens.forEach(t=> counts[t]=(counts[t]||0)+1);
      return vocab.map(t => (counts[t]||0)/Math.sqrt(tokens.length) * idf[t]);
    }

    function tokenize(s){ return (s.toLowerCase().match(/[a-z0-9]+/g)||[]) }

    function buildVocab(data){
      const text = [
        ...data.tests.map(t=> `${t.title} ${t.status} ${t.error||''}`),
        'passed fail error flaky duration time count tests'
      ].join(' ');
      const uniq = Array.from(new Set(tokenize(text)));
      return uniq.slice(0, 24); // small vocab for visuals
    }

    function rand(min,max){ return Math.random()*(max-min)+min }

    // Enhanced bar animation with stagger effect
    async function animateBars(vecA, vecB){
      const pick = [0,2,4,6,8,10].map(i=> [vecA[i]||0, vecB[i+1]||0]);
      const maxVal = Math.max(...pick.flat()) || 1;

      // Animate bars with stagger
      for(let i = 0; i < bars.length; i++){
        const bar = bars[i];
        const h = Math.min(1, (pick[i][0] + pick[i][1]) / maxVal);

        setTimeout(() => {
          bar.classList.add('animating');
          bar.style.transform = `scaleY(${0.15 + 0.8*h})`;
        }, i * 100);
      }

      // Remove animating class after animation
      setTimeout(() => {
        bars.forEach(bar => bar.classList.remove('animating'));
      }, 1000);
    }

    function guessIntent(query){
      const q = query.toLowerCase();
      // Simple NB-like scoring by token overlap with examples
      const scores = intents.map(it=>{
        const score = it.ex.reduce((s,ex)=> s + (q.includes(ex) ? 1 : 0), 0);
        return {id:it.id, label:it.label, score};
      }).sort((a,b)=> b.score - a.score);
      return scores;
    }

    // Enhanced step activation with progress tracking
    function setActive(stepIndex) {
      steps.forEach((step, i) => {
        step.classList.remove('active', 'loading');
        if(i < stepIndex) step.classList.add('completed');
        else step.classList.remove('completed');
      });

      if(stepIndex < steps.length) {
        steps[stepIndex].classList.add('active', 'loading');
      }

      // Activate arrows up to current step
      arrows.forEach((arrow, i) => {
        if(i < stepIndex) arrow.classList.add('active');
        else arrow.classList.remove('active');
      });

      // Update progress bar
      const progress = ((stepIndex + 1) / steps.length) * 100;
      progressBar.style.width = `${progress}%`;

      currentStep = stepIndex;
    }

    // Enhanced chip rendering with animations
    function renderChips(scores){
      chipsBox.innerHTML = '';
      scores.forEach((s,i)=>{
        const el = document.createElement('div');
        el.className = 'chip';
        el.textContent = `${s.label} (${s.score.toFixed(2)})`;
        if(i===0) el.classList.add('hit');

        // Stagger chip appearance
        el.style.opacity = '0';
        el.style.transform = 'translateY(10px)';
        chipsBox.appendChild(el);

        setTimeout(() => {
          el.style.transition = 'all 0.3s ease';
          el.style.opacity = '1';
          el.style.transform = 'translateY(0)';
        }, i * 100);
      });
    }

    // Enhanced run function with better animations and error handling
    async function run(){
      if(isRunning) return;
      isRunning = true;

      // Reset visuals
      reset();
      botOut.innerHTML = '🔄 Processing your request...';

      let data;
      try{
        data = JSON.parse(jsonIn.value);
      } catch(e){
        alert('❌ Invalid JSON format. Please check your test data.');
        isRunning = false;
        return;
      }

      try {
        // Step 1: Data Input with typing effect
        setActive(0);
        await typeText(jsonView, pretty({summary:data.summary, tests:data.tests.slice(0,3)}));
        await sleep(1000);

        // Step 2: TF‑IDF Vectorization
        setActive(1);
        const vocab = buildVocab(data);
        const dataTokens = buildVocab(data).map(x=>x);
        const dataVec = tfidfVector(dataTokens, vocab);
        const queryTokens = tokenize(qEl.value);
        const queryVec = tfidfVector(queryTokens, vocab);

        await animateBars(dataVec, queryVec);
        const sim = cosineSimilarity(dataVec, queryVec);
        simScore.textContent = `Similarity Score: ${(sim*100).toFixed(1)}%`;
        await sleep(1200);

        // Step 3: Intent Classification
        setActive(2);
        const scores = guessIntent(qEl.value).map((s,i)=> ({...s, score: s.score || (i? Math.max(0, .8 - i*0.15): .95) }));
        renderChips(scores);
        intentScore.textContent = `Best Match: ${scores[0].label} (${(scores[0].score*100).toFixed(1)}%)`;
        await sleep(1200);

        // Step 4: Response Generation with typing effect
        setActive(3);
        const top = intents.find(it=> it.id===scores[0].id) || intents[0];
        const msg = top.tpl(data);
        await typeText(botOut, msg, true);
        await sleep(1000);

        // Step 5: Analytics
        setActive(4);
        const extra = {
          pass_rate: ((data.summary.passed)/(data.summary.passed+data.summary.failed) * 100).toFixed(1) + '%',
          total_tests: data.summary.passed + data.summary.failed + (data.summary.skipped||0),
          avg_duration: Math.round(data.summary.duration_ms / (data.summary.passed + data.summary.failed)) + 'ms',
          status: data.summary.failed === 0 ? '✅ All Good' : '⚠️ Issues Found'
        };
        await typeText(insight, pretty(extra));

        // Final step completion
        steps[4].classList.remove('loading');
        await sleep(500);

      } catch(error) {
        console.error('Pipeline error:', error);
        botOut.innerHTML = '❌ Something went wrong. Please try again.';
      }

      isRunning = false;
    }

    // Typing effect for better presentation
    async function typeText(element, text, isHTML = false) {
      element.textContent = '';
      element.innerHTML = '';

      if(isHTML) {
        // For HTML content, show it all at once with fade-in
        element.style.opacity = '0';
        element.innerHTML = text;
        element.style.transition = 'opacity 0.5s ease';
        element.style.opacity = '1';
        await sleep(500);
      } else {
        // For plain text, type character by character
        for(let i = 0; i < text.length; i++) {
          element.textContent += text[i];
          await sleep(20);
        }
      }
    }

    // Enhanced reset with smooth transitions
    function reset(){
      if(isRunning) return;

      steps.forEach(step => {
        step.classList.remove('active', 'loading', 'completed');
      });
      arrows.forEach(arrow => arrow.classList.remove('active'));

      jsonView.textContent = '(ready to process...)';
      simScore.textContent = 'Similarity Score: —';
      intentScore.textContent = 'Best Match: —';
      chipsBox.innerHTML = '';
      botOut.innerHTML = 'Ready to help! Ask me anything about your tests.';
      insight.textContent = '(insights will appear here)';

      bars.forEach(bar => {
        bar.style.transform = 'scaleY(.15)';
        bar.classList.remove('animating');
      });

      progressBar.style.width = '0%';
      currentStep = 0;
    }

    // Auto-demo with different questions
    const demoQuestions = [
      "how many tests passed?",
      "what tests failed?",
      "how long did tests take?",
      "are there any flaky tests?"
    ];

    let demoIndex = 0;
    async function autoDemo() {
      if(isRunning) return;

      qEl.value = demoQuestions[demoIndex];
      demoIndex = (demoIndex + 1) % demoQuestions.length;

      await run();

      // Auto-reset after 3 seconds and run next demo
      setTimeout(async () => {
        if(!isRunning) {
          reset();
          setTimeout(() => autoDemo(), 1000);
        }
      }, 3000);
    }

    // Enhanced event listeners
    document.getElementById('run').addEventListener('click', run);
    document.getElementById('reset').addEventListener('click', reset);
    document.getElementById('autoplay').addEventListener('click', autoDemo);

    document.getElementById('pretty').addEventListener('click', ()=>{
      try{
        const o = JSON.parse(jsonIn.value);
        jsonIn.value = pretty(o);
      } catch(e) {
        alert('❌ Invalid JSON format');
      }
    });

    document.getElementById('minify').addEventListener('click', ()=>{
      try{
        const o = JSON.parse(jsonIn.value);
        jsonIn.value = JSON.stringify(o);
      } catch(e) {
        alert('❌ Invalid JSON format');
      }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if(e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        run();
      }
      if(e.key === 'Escape') {
        reset();
      }
    });

    // Initialize
    reset();
  </script>
</body>
</html>
